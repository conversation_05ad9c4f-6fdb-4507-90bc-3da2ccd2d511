#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/cli-service/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/cli-service/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/cli-service/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/cli-service/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/cli-service/bin/vue-cli-service.js" "$@"
else
  exec node  "$basedir/../.pnpm/@vue+cli-service@3.5.3_babe_0b6ae84985824b658d8f2cce3e2b2dad/node_modules/@vue/cli-service/bin/vue-cli-service.js" "$@"
fi
