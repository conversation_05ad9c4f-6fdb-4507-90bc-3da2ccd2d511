#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/run-node@1.0.0/node_modules/run-node/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/run-node@1.0.0/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/run-node@1.0.0/node_modules/run-node/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/run-node@1.0.0/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/bash" ]; then
  exec "$basedir/bash"  "$basedir/../run-node/run-node" "$@"
else
  exec bash  "$basedir/../run-node/run-node" "$@"
fi
