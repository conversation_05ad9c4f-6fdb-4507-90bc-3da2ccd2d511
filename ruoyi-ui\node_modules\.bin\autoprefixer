#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/autoprefixer@9.8.8/node_modules/autoprefixer/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/autoprefixer@9.8.8/node_modules/autoprefixer/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/autoprefixer@9.8.8/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/autoprefixer@9.8.8/node_modules/autoprefixer/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/autoprefixer@9.8.8/node_modules/autoprefixer/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/autoprefixer@9.8.8/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/autoprefixer@9.8.8/node_modules/autoprefixer/bin/autoprefixer" "$@"
else
  exec node  "$basedir/../.pnpm/autoprefixer@9.8.8/node_modules/autoprefixer/bin/autoprefixer" "$@"
fi
