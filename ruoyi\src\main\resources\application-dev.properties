ruoyi.name=RuoYi
ruoyi.version=2.3.0
ruoyi.copyrightYear=2019
ruoyi.demoEnabled=true
# 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
ruoyi.profile=./ruoyi/uploadPath
# 获取ip地址开关
ruoyi.addressEnabled=false

# 开发环境配置
# 服务器的HTTP端口，默认为8080
server.port=8089
# 应用的访问路径
server.servlet.context-path=/
# tomcat的URI编码
server.tomcat.uri-encoding=UTF-8
# tomcat最大线程数，默认为200
server.tomcat.max-threads=800
# Tomcat启动初始化的线程数，默认值25
server.tomcat.min-spare-threads=30

# 日志配置
logging.level.com.ruoyi=debug
logging.level.org.springframework=warn

# Spring配置
# 资源信息
# 国际化资源文件路径
spring.messages.basename=i18n/messages
#spring.profiles.active=druid
# 文件上传
# 单个文件大小
spring.servlet.multipart.max-file-size=10MB
# 设置总上传的文件大小
spring.servlet.multipart.max-request-size=20MB

# 服务模块
# 热部署开关
spring.devtools.restart.enabled=true

# redis 配置
spring.redis.host=*************
spring.redis.port=6379
spring.redis.database=11
spring.redis.password=wojinxaoRoot_wojin@8899

# 连接超时时间
spring.redis.timeout=10s
# 连接池中的最小空闲连接
spring.redis.lettuce.pool.min-idle=1
# 连接池中的最大空闲连接
spring.redis.lettuce.pool.max-idle=8
# 连接池的最大数据库连接数
spring.redis.lettuce.pool.max-active=8
#连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.lettuce.pool.max-wait=-1ms

## token配置
## 令牌自定义标识
#token.header=Authorization
## 令牌密钥
#token.secret=abcdefghijklmnopqrstuvwxyz
## 令牌有效期（默认30分钟）
#token.expireTime=60

# MyBatis配置
# 搜索指定包别名
mybatis.typeAliasesPackage=com.ruoyi.project.**.domain
# 配置mapper的扫描，找到所有的mapper.xml映射文件
mybatis.mapperLocations=classpath*:mybatis/**/*Mapper.xml
# 加载全局的配置文件
mybatis.configLocation=classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.supportMethodsArguments=true
pagehelper.params=count=countSql

# Swagger配置
# 是否开启swagger
swagger.enabled=true
# 请求前缀
swagger.pathMapping=/dev-api

# 防止XSS攻击
# 过滤开关
xss.enabled=true
# 排除链接（多个用逗号分隔）
xss.excludes=/system/notice/*
# 匹配链接
xss.urlPatterns=/system/*,/monitor/*,/tool/*

# 代码生成
# 作者
gen.author=ruoyi
# 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
gen.packageName=com.ruoyi.project.business
# 自动去除表前缀，默认是true
gen.autoRemovePre=false
# 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
gen.tablePrefix=sys_

# 创建租户账号默认的密码
# 账号默认为手机号，请自行修改
sys.default.pwd=123456.

#aliyun-oss相关配置,如果使用阿里云OSS存储文件，替换对应配置即可
aliyun.oss.endpoint = http://oss-cn-shanghai.aliyuncs.com
aliyun.oss.accessKeyId = xxxxxxxxxx
aliyun.oss.accessKeySecret = xxxxxxxxxx
aliyun.oss.defaultBucketName = xxxxxxxxxx
aliyun.oss.endpointContent = oss-cn-shanghai.aliyuncs.com

#####################druid######################
# 数据源配置
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
# 主库数据源
spring.datasource.druid.master.url=*****************************************************************************************************************************************************************************************
spring.datasource.druid.master.username=xiaochengxv
spring.datasource.druid.master.password=wojinRoot_wojin@888

# 从库数据源
# 从数据源开关/默认关闭
spring.datasource.druid.slave.enabled=false
spring.datasource.druid.slave.url=
spring.datasource.druid.slave.username=
spring.datasource.druid.slave.password=
#####################druid######################

# token名称 (同时也是cookie名称)
sa-token.token-name=Authorization
# token前缀
sa-token.token-prefix=Bearer
# 后端不管理token在cookie的存储，满足完全的前后端分离。
sa-token.is-read-cookie=false
# token有效期，单位s 默认30天, -1代表永不过期，此处设置30天。（sa-token.is-concurrent=false时建议有效期长一些，通过sa-token.activity-timeout来控制登录有效期）
sa-token.timeout=2592000
# token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
sa-token.activity-timeout=3600
# 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
sa-token.is-concurrent=false
# 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
sa-token.is-share=false
# token风格（内置的token-style，此系统整合了jwt插件(Simple简单模式)，用不到这配置）
sa-token.token-style=tik
# 是否输出操作日志
sa-token.is-log=false
sa-token.jwt-secret-key=0982c36033b7461ba1b7fc42228f8616

# Sa-Token插件：配置Sa-Token单独使用的Redis database连接，权限缓存与业务缓存分离。
# Redis数据库索引
sa-token.alone-redis.database=10
# Redis服务器地址
sa-token.alone-redis.host=*************
# Redis服务器连接端口
sa-token.alone-redis.port=6379
# Redis服务器连接密码（默认为空）
sa-token.alone-redis.password=wojinxaoRoot_wojin@8899
# 连接超时时间
sa-token.alone-redis.timeout=10s
# 连接池最大连接数
sa-token.alone-redis.lettuce.pool.max-active=200
# 连接池最大阻塞等待时间（使用负值表示没有限制）
sa-token.alone-redis.lettuce.pool.max-wait=-1ms
# 连接池中的最大空闲连接
sa-token.alone-redis.lettuce.pool.max-idle=10
# 连接池中的最小空闲连接
sa-token.alone-redis.lettuce.pool.min-idle=0

spring.quartz.properties.org.quartz.jobStore.tablePrefix = qrtz_
