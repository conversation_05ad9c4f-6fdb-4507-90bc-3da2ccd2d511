#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\webpack-dev-server@3.11.3_webpack@4.28.4\node_modules\webpack-dev-server\bin\node_modules;E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\webpack-dev-server@3.11.3_webpack@4.28.4\node_modules\webpack-dev-server\node_modules;E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\webpack-dev-server@3.11.3_webpack@4.28.4\node_modules;E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules/webpack-dev-server/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules/webpack-dev-server/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" $args
  } else {
    & "node$exe"  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
