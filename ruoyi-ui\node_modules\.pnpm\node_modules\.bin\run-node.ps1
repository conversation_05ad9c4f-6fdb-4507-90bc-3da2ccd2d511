#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\run-node@1.0.0\node_modules\run-node\node_modules;E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\run-node@1.0.0\node_modules;E:\IdeaProject\kindergarten_accounting\ruoyi-ui\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/run-node@1.0.0/node_modules/run-node/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/run-node@1.0.0/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/bash$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/bash$exe"  "$basedir/../run-node/run-node" $args
  } else {
    & "$basedir/bash$exe"  "$basedir/../run-node/run-node" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "bash$exe"  "$basedir/../run-node/run-node" $args
  } else {
    & "bash$exe"  "$basedir/../run-node/run-node" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
