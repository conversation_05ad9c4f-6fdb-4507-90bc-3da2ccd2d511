#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/staged-git-files@1.1.2/node_modules/staged-git-files/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/staged-git-files@1.1.2/node_modules/staged-git-files/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/staged-git-files@1.1.2/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/staged-git-files@1.1.2/node_modules/staged-git-files/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/staged-git-files@1.1.2/node_modules/staged-git-files/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/staged-git-files@1.1.2/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../staged-git-files/bin/cli.js" "$@"
else
  exec node  "$basedir/../staged-git-files/bin/cli.js" "$@"
fi
