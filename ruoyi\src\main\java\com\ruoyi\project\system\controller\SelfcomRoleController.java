package com.ruoyi.project.system.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.text.CharSequenceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;
import com.ruoyi.framework.security.LoginUser;
import com.ruoyi.project.system.domain.SysRole;
import com.ruoyi.project.system.service.ISysRoleService;

/**
 * 角色信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/selfcom/role")
public class SelfcomRoleController extends BaseController {
    @Autowired
    private ISysRoleService roleService;

    @SaCheckPermission("selfcom:role:list")
    @GetMapping("/list")
    public TableDataInfo list(SysRole role) {
        role.setComId(SecurityUtils.getCurrComId());
        startPage();
        List<SysRole> list = roleService.selectRoleList(role);
        return getDataTable(list);
    }

    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("selfcom:role:export")
    @GetMapping("/export")
    public AjaxResult export(SysRole role) {
        List<SysRole> list = roleService.selectRoleList(role);
        ExcelUtil<SysRole> util = new ExcelUtil<SysRole>(SysRole.class);
        return util.exportExcel(list, "角色数据");
    }

    /**
     * 根据角色编号获取详细信息
     */
    @SaCheckPermission("selfcom:role:query")
    @GetMapping(value = "/{roleId}")
    public AjaxResult getInfo(@PathVariable Long roleId) {
        return AjaxResult.success(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @SaCheckPermission("selfcom:role:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysRole role) {
        try {
            // 调试信息
            logger.info("开始创建角色，角色名称: {}", role.getRoleName());

            // 获取登录用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();
            logger.info("登录用户: {}", loginUser != null ? loginUser.getUsername() : "null");

            if (loginUser != null && loginUser.getUser() != null) {
                logger.info("用户comId: {}", loginUser.getUser().getComId());
                logger.info("用户对象: {}", loginUser.getUser());
            }

            String comId = SecurityUtils.getCurrComId();
            logger.info("获取到的comId: {}", comId);

            if (StringUtils.isBlank(comId)) {
                logger.error("comId为空，无法创建角色");
                return AjaxResult.error("获取公司信息失败，请重新登录");
            }

            role.setComId(comId);

            if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
                return AjaxResult.error("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
            } else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role))) {
                return AjaxResult.error("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
            }
            role.setCreateBy(SecurityUtils.getUsername());
            return toAjax(roleService.insertRole(role));

        } catch (Exception e) {
            logger.error("创建角色失败", e);
            return AjaxResult.error("创建角色失败: " + e.getMessage());
        }
    }

    /**
     * 修改保存角色
     */
    @SaCheckPermission("selfcom:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysRole role) {
        if (CharSequenceUtil.isBlank(role.getComId())) {
            role.setComId(SecurityUtils.getCurrComId());
        }
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return AjaxResult.error("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role))) {
            return AjaxResult.error("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        role.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(roleService.updateRole(role));
    }

    /**
     * 修改保存数据权限
     */
    @SaCheckPermission("selfcom:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public AjaxResult dataScope(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        return toAjax(roleService.authDataScope(role));
    }

    /**
     * 状态修改
     */
    @SaCheckPermission("selfcom:role:edit")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        role.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @SaCheckPermission("selfcom:role:remove")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds) {
        return toAjax(roleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表
     */
    @SaCheckPermission("selfcom:role:query")
    @GetMapping("/optionselect")
    public AjaxResult optionselect() {
        return AjaxResult.success(roleService.selectRoleAll());
    }
}
