{"name": "babel-loader", "version": "10.0.0", "description": "babel module loader for webpack", "files": ["lib"], "main": "lib/index.js", "engines": {"node": "^18.20.0 || ^20.10.0 || >=22.0.0"}, "dependencies": {"find-up": "^5.0.0"}, "peerDependencies": {"@babel/core": "^7.12.0", "webpack": ">=5.61.0"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.3", "@babel/eslint-parser": "^7.23.3", "@babel/preset-env": "^7.23.3", "c8": "^10.1.2", "eslint": "^9.6.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "globals": "^15.8.0", "husky": "^9.1.5", "lint-staged": "^15.2.9", "prettier": "^3.0.0", "webpack": "^5.93.0"}, "scripts": {"clean": "node ./scripts/rimraf.mjs lib", "build": "babel src/ --out-dir lib/ --copy-files", "format": "prettier --write --trailing-comma all 'src/**/*.js' 'test/**/*.test.js' 'test/helpers/*.js' && prettier --write --trailing-comma es5 'scripts/*.js'", "lint": "eslint src test", "precommit": "lint-staged", "prepublish": "yarn run clean && yarn run build", "preversion": "yarn run test", "test": "yarn run lint && yarn run build --source-maps && c8 yarn run test-only", "test-only": "node --test test/**/*.test.js"}, "resolutions": {"minipass": "6.0.2"}, "repository": {"type": "git", "url": "https://github.com/babel/babel-loader.git"}, "keywords": ["webpack", "loader", "babel", "es6", "transpiler", "module"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/babel/babel-loader/issues"}, "homepage": "https://github.com/babel/babel-loader", "nyc": {"all": true, "include": ["src/**/*.js"], "reporter": ["text", "json"], "sourceMap": false, "instrument": false}, "lint-staged": {"scripts/*.js": ["prettier --trailing-comma es5 --write", "git add"], "src/**/*.js": ["prettier --trailing-comma all --write", "git add"], "test/**/*.test.js": ["prettier --trailing-comma all --write", "git add"], "test/helpers/*.js": ["prettier --trailing-comma all --write", "git add"], "package.json": ["node ./scripts/yarn-install.js", "git add yarn.lock"]}, "packageManager": "yarn@3.6.4"}