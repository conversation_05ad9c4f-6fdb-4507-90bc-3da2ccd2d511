#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules/webpack-dev-server/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules/webpack-dev-server/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules/webpack-dev-server/bin/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules/webpack-dev-server/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/webpack-dev-server@3.11.3_webpack@4.28.4/node_modules:/mnt/e/IdeaProject/kindergarten_accounting/ruoyi-ui/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" "$@"
else
  exec node  "$basedir/../webpack-dev-server/bin/webpack-dev-server.js" "$@"
fi
