spring.profiles.active=dev

spring.application.name=multi_tenant

#####################druid###########################
# 初始连接数
spring.datasource.druid.initialSize=5
# 最小连接池数量
spring.datasource.druid.minIdle=10
# 最大连接池数量
spring.datasource.druid.maxActive=20
# 配置获取连接等待超时的时间
spring.datasource.druid.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.druid.minEvictableIdleTimeMillis=300000
# 配置一个连接在池中最大生存的时间，单位是毫秒
spring.datasource.druid.maxEvictableIdleTimeMillis=900000
# 配置检测连接是否有效
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.webStatFilter.enabled=true
spring.datasource.druid.statViewServlet.enabled=true
# 设置白名单，不填则允许所有访问
spring.datasource.druid.statViewServlet.allow=
spring.datasource.druid.statViewServlet.url-pattern=/druid/*
# 控制台管理用户名和密码
spring.datasource.druid.statViewServlet.login-username=
spring.datasource.druid.statViewServlet.login-password=

# 慢SQL记录
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=1000
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.wall.config.multi-statement-allow=true
#####################druid###########################

spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER